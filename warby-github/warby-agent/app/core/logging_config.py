from loguru import logger
import sys
import os

def configure_logging():
    """Configure logging based on environment"""
    # Get environment (default to development)
    environment = os.getenv("ENVIRONMENT", "production").lower()

    # Remove default logger
    logger.remove()

    # Environment-specific configuration
    if environment in ["production", "prod"]:
        # Production: INFO level, minimal sensitive data
        console_level = "INFO"
        file_level = "INFO"
        enable_backtrace = False
        enable_diagnose = False
        log_format = "{time:YYYY-MM-DD HH:mm:ss.SSSSSS} | {level} | {message}"
    else:
        # Development/Testing: DEBUG level, full diagnostics
        console_level = "DEBUG"
        file_level = "DEBUG"
        enable_backtrace = True
        enable_diagnose = True
        log_format = "<green>{time}</green> | <level>{level}</level> | <cyan>{message}</cyan>"

    # Console logging
    logger.add(
        sys.stdout,
        level=console_level,
        format=log_format
    )

    # File logging (ensure logs directory exists)
    os.makedirs("logs", exist_ok=True)
    logger.add(
        "logs/app.log",
        rotation="1 week",
        level=file_level,
        backtrace=enable_backtrace,
        diagnose=enable_diagnose,
        format="{time:YYYY-MM-DD HH:mm:ss.SSSSSS} | {level} | {message}"
    )

    logger.info(f"Logging configured for environment: {environment}")

# Configure logging on import
configure_logging()
