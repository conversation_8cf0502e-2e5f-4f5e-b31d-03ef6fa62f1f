from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, field_validator
from typing import Optional
from loguru import logger
from app.core.config import settings

router = APIRouter()

class QueryRequest(BaseModel):
    question: str
    topic: Optional[str] = None  # Optional topic filter for metadata search

    @field_validator('question')
    @classmethod
    def question_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Question cannot be empty')
        if len(v.strip()) > 10000:  # Reasonable limit
            raise ValueError('Question is too long (max 10000 characters)')
        return v.strip()

    @field_validator('topic')
    @classmethod
    def topic_validator(cls, v):
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None  # Empty string becomes None
            if len(v) > 100:  # Reasonable limit for topic
                raise ValueError('Topic is too long (max 100 characters)')
        return v

class QueryResponse(BaseModel):
    question: str
    topic: Optional[str] = None
    results: list
    status: str

@router.post("/query_askhr", response_model=QueryResponse)
async def query_askhr(payload: QueryRequest):
    """Query askhr_collection with the provided question"""
    try:
        # Validate collection is configured
        if not settings.MILVUS_ASKHR_COLLECTION:
            raise HTTPException(
                status_code=500,
                detail="AskHR collection not configured. Please set MILVUS_ASKHR_COLLECTION environment variable."
            )

        # Import and get singleton instance for better performance
        from app.services.milvus_service import get_milvus_service
        milvus_service = get_milvus_service(settings.MILVUS_ASKHR_COLLECTION)

        # Execute query with optional topic filtering
        results = milvus_service.query(payload.question, topic=payload.topic)

        logger.info(f"AskHR query processed successfully for question: {payload.question[:50]}... {f'with topic: {payload.topic}' if payload.topic else ''}")

        return QueryResponse(
            question=payload.question,
            topic=payload.topic,
            results=results,
            status="success"
        )

    except ValueError as ve:
        logger.error(f"Validation error: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except ConnectionError as e:
        logger.error(f"Milvus connection error: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Service temporarily unavailable: {str(e)}"
        )
    except ImportError as ie:
        logger.error(f"Dependency error: {ie}")
        raise HTTPException(
            status_code=500,
            detail="Required dependencies not available. Please check server configuration."
        )
    except RuntimeError as re:
        logger.error(f"Runtime error: {re}")
        raise HTTPException(status_code=500, detail=str(re))
    except Exception as e:
        logger.error(f"AskHR query failed: {e}")
        raise HTTPException(status_code=500, detail=f"AskHR query failed: {str(e)}")

@router.post("/query_askit", response_model=QueryResponse)
async def query_askit(payload: QueryRequest):
    """Query askit_collection with the provided question"""
    try:
        # Validate collection is configured
        if not settings.MILVUS_ASKIT_COLLECTION:
            raise HTTPException(
                status_code=500,
                detail="AskIT collection not configured. Please set MILVUS_ASKIT_COLLECTION environment variable."
            )

        # Import and get singleton instance for better performance
        from app.services.milvus_service import get_milvus_service
        milvus_service = get_milvus_service(settings.MILVUS_ASKIT_COLLECTION)

        # Execute query with optional topic filtering
        results = milvus_service.query(payload.question, topic=payload.topic)

        logger.info(f"AskIT query processed successfully for question: {payload.question[:50]}... {f'with topic: {payload.topic}' if payload.topic else ''}")

        return QueryResponse(
            question=payload.question,
            topic=payload.topic,
            results=results,
            status="success"
        )

    except ValueError as ve:
        logger.error(f"Validation error: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except ConnectionError as e:
        logger.error(f"Milvus connection error: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Service temporarily unavailable: {str(e)}"
        )
    except ImportError as ie:
        logger.error(f"Dependency error: {ie}")
        raise HTTPException(
            status_code=500,
            detail="Required dependencies not available. Please check server configuration."
        )
    except RuntimeError as re:
        logger.error(f"Runtime error: {re}")
        raise HTTPException(status_code=500, detail=str(re))
    except Exception as e:
        logger.error(f"AskIT query failed: {e}")
        raise HTTPException(status_code=500, detail=f"AskIT query failed: {str(e)}")
