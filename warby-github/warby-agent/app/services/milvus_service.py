from pymilvus import connections, Collection, utility
from app.core.config import settings
from loguru import logger
from pathlib import Path
import time
import os

class MilvusService:
    # Class-level cache for embedding model and connections
    _embed_model_cache = None
    _embed_model_name = None  # Track which model is cached
    _embed_model_dimension = None  # Track model dimension
    _model_initialization_lock = False  # Prevent concurrent initialization
    _connection_cache = {}
    _collection_cache = {}

    def __init__(self, collection_name=None):
        self.collection = None
        self.collection_name = collection_name
        self.is_connected = False
        self.collection_exists = False
        self.embed_model = None
        self.connection_retries = 3
        self.retry_delay = 2  # seconds
        self.collection_loaded = False  # Track if collection is already loaded
        # Don't connect immediately during import to avoid startup failures

    @classmethod
    def is_model_cached(cls, model_name: str = None) -> bool:
        """Check if embedding model is already cached and healthy"""
        if cls._embed_model_cache is None:
            return False

        # If specific model name provided, check if it matches cached model
        if model_name and cls._embed_model_name != model_name:
            return False

        # Test model health by trying a simple encoding
        try:
            test_embedding = cls._embed_model_cache.encode("test")
            return len(test_embedding) > 0
        except Exception as e:
            logger.debug(f"Cached model is unhealthy: {e}")
            cls._clear_model_cache()
            return False

    @classmethod
    def _clear_model_cache(cls):
        """Clear cached embedding model"""
        cls._embed_model_cache = None
        cls._embed_model_name = None
        cls._embed_model_dimension = None
        cls._model_initialization_lock = False

    @classmethod
    def get_cached_model_info(cls) -> dict:
        """Get information about cached model"""
        return {
            "model_name": cls._embed_model_name,
            "dimension": cls._embed_model_dimension,
            "is_cached": cls._embed_model_cache is not None,
            "is_healthy": cls.is_model_cached()
        }

    def _check_connection_exists(self, alias: str = "default") -> bool:
        """Check if Milvus connection already exists and is healthy"""
        try:
            if connections.has_connection(alias):
                # Test connection health by getting server version
                version = utility.get_server_version()
                logger.debug(f"Existing connection is healthy (version: {version})")
                return True
        except Exception as e:
            logger.debug(f"Existing connection is unhealthy: {e}")
            # Remove unhealthy connection
            try:
                connections.remove_connection(alias)
            except:
                pass
        return False

    def _create_connection_with_timeout(self, connection_params: dict, timeout: int = None) -> bool:
        """Create Milvus connection with timeout handling"""
        timeout = timeout or settings.MILVUS_CONNECTION_TIMEOUT

        try:
            # Set connection timeout in parameters
            connection_params["timeout"] = timeout
            connections.connect(**connection_params)

            # Verify connection by getting server version
            version = utility.get_server_version()
            logger.info(f"Connected to Milvus (version {version})")
            return True

        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False

    def _initialize_collection(self):
        """Initialize collection after connection is established"""
        try:
            # Check if collection exists
            collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
            if not collection_to_check:
                raise ValueError("No collection name specified")

            if utility.has_collection(collection_to_check):
                self.collection = Collection(collection_to_check)
                self.collection_exists = True
                logger.info(f"Connected to Milvus collection: {collection_to_check}")

                # Validate collection schema
                self._validate_collection_schema()
            else:
                self.collection_exists = False
                logger.warning(f"Collection '{collection_to_check}' does not exist")

            self.is_connected = True

        except Exception as e:
            logger.error(f"Failed to initialize collection: {e}")
            raise

    def connect_milvus(self):
        """Connect to Milvus database with optimized connection management"""
        connection_alias = "default"

        # Check if connection already exists and is healthy
        if self._check_connection_exists(connection_alias):
            logger.debug("Reusing existing healthy Milvus connection")
            # Still need to initialize collection if not done
            if not self.is_connected:
                self._initialize_collection()
            return

        max_retries = settings.MILVUS_CONNECTION_RETRY_ATTEMPTS
        retry_delay = settings.MILVUS_CONNECTION_RETRY_DELAY

        for attempt in range(max_retries):
            try:
                logger.info(f"Attempting to connect to Milvus (attempt {attempt + 1}/{max_retries})")

                # Disconnect any existing unhealthy connection
                try:
                    connections.disconnect(connection_alias)
                except Exception:
                    pass  # Ignore if no connection exists

                # Prepare connection parameters
                connection_params = {
                    "alias": connection_alias,
                    "host": settings.MILVUS_HOST,
                    "port": settings.MILVUS_PORT,
                    "user": settings.MILVUS_USER,
                    "password": settings.MILVUS_PASSWORD,
                    "secure": True
                }

                # Add certificate if configured
                if hasattr(settings, 'MILVUS_CERT') and settings.MILVUS_CERT:
                    connection_params["server_pem_path"] = settings.MILVUS_CERT

                # Create connection with timeout
                if self._create_connection_with_timeout(connection_params):
                    self._initialize_collection()
                    return  # Success, exit retry loop

            except Exception as e:
                logger.error(f"Failed to connect to Milvus (attempt {attempt + 1}/{max_retries}): {e}")
                self.is_connected = False
                self.collection_exists = False

                if attempt < max_retries - 1:
                    logger.info(f"Retrying connection in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error("All connection attempts failed")
                    # Don't raise the exception to allow the app to start

    def _validate_collection_schema(self):
        """Validate that the collection has the expected schema"""
        if not self.collection:
            return

        try:
            # Get collection schema
            schema = self.collection.schema
            field_names = [field.name for field in schema.fields]

            # Expected fields for the search query
            expected_fields = [
                "vector", "chunk_text", "file_name", "page_number",
                "chunk_id", "mime_type", "modified_time", "last_modified_by",
                "description_url", "web_url", "drive_path", "doc_id", "table_id",
                "topic", "hyperlinks"
            ]

            missing_fields = [field for field in expected_fields if field not in field_names]
            if missing_fields:
                logger.warning(f"Collection schema missing expected fields: {missing_fields}")

        except Exception as e:
            logger.warning(f"Could not validate collection schema: {e}")

    def _get_collection_vector_dimension(self):
        """Get the vector dimension from the collection schema

        Returns:
            tuple: (dimension: int|None, error: str|None)
                - (768, None) - Success: found 768D vector field
                - (None, None) - Success: no vector field found
                - (None, "error message") - Error: connection/schema issue
        """
        try:
            if self.collection is None:
                return None, "Collection not initialized"

            schema = self.collection.schema
            for field in schema.fields:
                if field.name == "vector" and field.dtype.name == "FLOAT_VECTOR":
                    dim = field.params.get("dim")
                    if dim:
                        logger.debug(f"Found vector field with dimension: {dim}")
                        return dim, None
                    else:
                        return None, "Vector field found but dimension not specified"

            # No vector field found - this might be expected for some collections
            logger.debug("No vector field found in collection schema")
            return None, None

        except Exception as e:
            error_msg = f"Could not determine vector dimension from collection: {e}"
            logger.error(error_msg)
            return None, error_msg

    def _initialize_embedding_model(self):
        """Initialize the sentence transformer model with optimized caching and dynamic model selection"""

        # Check if model initialization is in progress (prevent concurrent initialization)
        if MilvusService._model_initialization_lock:
            logger.debug("Model initialization in progress, waiting...")
            # Wait for initialization to complete
            max_wait = 30  # seconds
            wait_time = 0
            while MilvusService._model_initialization_lock and wait_time < max_wait:
                time.sleep(1)
                wait_time += 1

            if MilvusService._embed_model_cache is not None:
                self.embed_model = MilvusService._embed_model_cache
                return self.embed_model

        # Check if we have a forced model and it's already cached
        forced_model = settings.FORCE_EMBEDDING_MODEL
        if forced_model and MilvusService.is_model_cached(forced_model):
            logger.debug(f"Using cached forced model: {forced_model}")
            self.embed_model = MilvusService._embed_model_cache
            return self.embed_model

        # Check class-level cache for any healthy model
        if MilvusService.is_model_cached():
            logger.debug(f"Using cached model: {MilvusService._embed_model_name}")
            self.embed_model = MilvusService._embed_model_cache
            return self.embed_model

        # If we have an instance-level model, check if it's still valid
        if self.embed_model is not None:
            try:
                test_embedding = self.embed_model.encode("test")
                if len(test_embedding) > 0:
                    return self.embed_model
            except Exception:
                self.embed_model = None

        # Set initialization lock to prevent concurrent loading
        MilvusService._model_initialization_lock = True

        try:
            # Import here to avoid circular imports
            from sentence_transformers import SentenceTransformer

            # Check if model cache directory exists and is writable
            cache_dir = os.path.expanduser("~/.cache/torch/sentence_transformers")
            os.makedirs(cache_dir, exist_ok=True)

            # Fast path: If specific model is forced, skip auto-detection
            if hasattr(settings, 'FORCE_EMBEDDING_MODEL') and settings.FORCE_EMBEDDING_MODEL:
                forced_model = settings.FORCE_EMBEDDING_MODEL
                logger.info(f"Using forced embedding model: {forced_model}")
                self.embed_model = SentenceTransformer(forced_model)

                # Cache at class level with metadata
                MilvusService._embed_model_cache = self.embed_model
                MilvusService._embed_model_name = forced_model
                MilvusService._embed_model_dimension = len(self.embed_model.encode("test"))

                logger.info(f"Forced embedding model loaded and cached successfully (dim: {MilvusService._embed_model_dimension})")
                return self.embed_model

            # Get vector dimension from collection to determine which model to use
            vector_dim, dim_error = self._get_collection_vector_dimension()

            if dim_error:
                logger.error(f"Failed to detect collection vector dimension: {dim_error}")
                logger.warning("Proceeding with default model selection...")
            elif vector_dim:
                logger.info(f"Detected collection vector dimension: {vector_dim}")
            else:
                logger.info("No vector dimension detected from collection schema")

            # Define the 3 core models you want to support
            # Order: Primary model first, then fallbacks
            model_options = [
                {
                    "name": "ibm-granite/granite-embedding-278m-multilingual",
                    "dimension": 768,
                    "description": "IBM Granite multilingual embedding model (primary)"
                },
                {
                    "name": "sentence-transformers/all-mpnet-base-v2",
                    "dimension": 768,
                    "description": "Sentence Transformers all-mpnet-base-v2 (768D fallback)"
                },
                {
                    "name": "sentence-transformers/all-MiniLM-L6-v2",
                    "dimension": 384,
                    "description": "Sentence Transformers all-MiniLM-L6-v2 (384D fallback)"
                }
            ]

            # Dynamic model selection: try models in order and test actual dimensions
            logger.info(f"Collection expects {vector_dim}D vectors, trying models dynamically...")

            # If we know the expected dimension, prioritize matching models
            if vector_dim:
                # Sort models by dimension match first
                sorted_models = []
                # Add models that match expected dimension first
                for model in model_options:
                    if model["dimension"] == vector_dim:
                        sorted_models.append(model)
                # Add remaining models as fallbacks
                for model in model_options:
                    if model["dimension"] != vector_dim:
                        sorted_models.append(model)
                models_to_try = sorted_models
            else:
                # No dimension info, try in default order
                models_to_try = model_options

            logger.info(f"Will try {len(models_to_try)} models in order...")

            # Try each model in order
            for i, current_model in enumerate(models_to_try):

                try:
                    logger.info(f"Loading embedding model: {current_model['name']} (expected dim: {current_model['dimension']})")
                    self.embed_model = SentenceTransformer(current_model["name"])

                    # Test the model with a sample text to verify it works
                    test_embedding = self.embed_model.encode("test")
                    actual_dim = len(test_embedding)

                    logger.info(f"Model loaded successfully. Actual dimension: {actual_dim}")

                    # If we have collection dimension, verify it matches
                    if vector_dim and actual_dim != vector_dim:
                        logger.error(f"DIMENSION MISMATCH: Model '{current_model['name']}' produces {actual_dim}D vectors, but collection expects {vector_dim}D vectors")
                        logger.error(f"This suggests your collection was created with a different embedding model")
                        logger.error(f"Expected for your setup: IBM Granite model should produce 768D vectors")
                        if i < len(models_to_try) - 1:  # Try next model if not the last one
                            logger.info("Trying next model...")
                            continue
                        else:
                            # Last model also failed, but continue anyway with warning
                            logger.warning(f"Using model with dimension mismatch as last resort: {current_model['name']}")
                    else:
                        logger.info(f"✅ Perfect match: Model dimension ({actual_dim}) matches collection dimension ({vector_dim})")

                    # Cache at class level for reuse across instances with metadata
                    MilvusService._embed_model_cache = self.embed_model
                    MilvusService._embed_model_name = current_model['name']
                    MilvusService._embed_model_dimension = actual_dim

                    logger.info(f"🎯 Embedding model cached successfully: {current_model['name']} (dim: {actual_dim})")
                    return self.embed_model

                except Exception as model_error:
                    logger.warning(f"Failed to load model {current_model['name']}: {model_error}")
                    if i < len(models_to_try) - 1:  # Try next model if not the last one
                        logger.info(f"Trying next model ({i+2}/{len(models_to_try)})...")
                        continue
                    else:
                        logger.error("All embedding models failed to load!")
                        logger.error("Models attempted:")
                        for j, model in enumerate(models_to_try):
                            logger.error(f"  {j+1}. {model['name']} (dim: {model['dimension']})")
                        raise model_error

        except ImportError as e:
            logger.error(f"sentence-transformers package not available: {e}")
            raise ImportError("sentence-transformers package is required but not installed")
        except Exception as e:
            logger.error(f"Failed to load any embedding model: {e}")
            raise RuntimeError(f"Could not initialize any embedding model: {e}")
        finally:
            # Always release the initialization lock
            MilvusService._model_initialization_lock = False

    def health_check(self) -> dict:
        """Check Milvus connection health"""
        try:
            # Try to connect if not connected
            if not self.is_connected:
                self.connect_milvus()

            if not self.is_connected:
                return {"status": "disconnected", "error": "Failed to connect to Milvus"}

            # Check collection status
            collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
            if not collection_to_check:
                return {"status": "error", "error": "No collection name specified"}

            if not self.collection_exists:
                return {
                    "status": "warning",
                    "collection": collection_to_check,
                    "error": f"Collection '{collection_to_check}' does not exist",
                    "message": "Collection needs to be created and populated with data"
                }

            # Try to get collection info
            if self.collection:
                try:
                    # Load collection to get accurate count
                    self.collection.load()
                    num_entities = self.collection.num_entities

                    # Check if embedding model can be loaded
                    try:
                        self._initialize_embedding_model()
                        model_status = "OK"
                    except Exception as model_error:
                        model_status = f"Error: {str(model_error)}"

                    return {
                        "status": "OK",
                        "collection": collection_to_check,
                        "num_entities": num_entities,
                        "embedding_model": model_status
                    }
                except Exception as collection_error:
                    logger.error(f"Error accessing collection: {collection_error}")
                    return {
                        "status": "error",
                        "collection": collection_to_check,
                        "error": f"Collection access error: {str(collection_error)}"
                    }
            else:
                return {"status": "error", "error": "Collection not initialized"}

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "error", "error": str(e)}

    def query(self, question: str, topic: str = None):
        """Query Milvus collection using vector similarity search with optional topic filtering"""
        # Validate input
        if not question or not question.strip():
            raise ValueError("Question cannot be empty")

        # Try to connect if not connected
        if not self.is_connected:
            self.connect_milvus()

        if not self.is_connected:
            raise ConnectionError("Failed to connect to Milvus")

        collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
        if not collection_to_check:
            raise ValueError("No collection name specified")

        if not self.collection_exists:
            raise ConnectionError(f"Collection '{collection_to_check}' does not exist. Please create and populate the collection first.")

        if not self.collection:
            raise ConnectionError("Collection not initialized")

        try:
            # Initialize embedding model with error handling
            embed_model = self._initialize_embedding_model()

            # Generate query embedding
            logger.debug(f"Generating embedding for query: {question[:100]}...")
            query_vector = embed_model.encode(question.strip()).tolist()

            if not query_vector:
                raise ValueError("Failed to generate query embedding")

            # Load collection for search only if not already loaded
            if not self.collection_loaded:
                logger.debug("Loading collection for search...")
                self.collection.load()
                self.collection_loaded = True
            else:
                logger.debug("Collection already loaded, skipping load operation")

            # Perform vector similarity search with error handling
            logger.debug("Performing vector similarity search...")

            # Use optimized search parameters from config
            search_params = {"metric_type": "COSINE", "params": {"nprobe": settings.MILVUS_NPROBE}}

            # Build topic filter expression if topic is provided
            topic_filter = None
            if topic and topic.strip():
                topic_filter = f'topic == "{topic.strip()}"'
                logger.debug(f"Applying topic filter: {topic_filter}")

            try:
                search_results = self.collection.search(
                    data=[query_vector],
                    anns_field="vector",
                    param=search_params,
                    limit=settings.MILVUS_SEARCH_LIMIT,
                    expr=topic_filter,  # Topic-based metadata filtering
                    output_fields=["chunk_text", "file_name", "page_number", "chunk_id", "mime_type", "modified_time", "last_modified_by", "description_url", "web_url", "drive_path", "doc_id", "table_id", "topic", "hyperlinks"]
                )
                logger.debug(f"Search successful with COSINE metric{' and topic filter' if topic_filter else ''}")
            except Exception as e:
                # Fallback to L2 if COSINE fails
                logger.debug(f"COSINE search failed: {e}, trying L2")
                try:
                    search_params = {"metric_type": "L2", "params": {"nprobe": settings.MILVUS_NPROBE}}
                    search_results = self.collection.search(
                        data=[query_vector],
                        anns_field="vector",
                        param=search_params,
                        limit=settings.MILVUS_SEARCH_LIMIT,
                        expr=topic_filter,  # Topic-based metadata filtering
                        output_fields=["chunk_text", "file_name", "page_number", "chunk_id", "mime_type", "modified_time", "last_modified_by", "description_url", "web_url", "drive_path", "doc_id", "table_id", "topic", "hyperlinks"]
                    )
                    logger.debug(f"Search successful with L2 metric{' and topic filter' if topic_filter else ''}")
                except Exception as e2:
                    raise RuntimeError(f"Both COSINE and L2 search failed. Last error: {e2}")

            # Validate search results
            if not search_results or len(search_results) == 0:
                logger.warning("No search results returned")
                return []

            # Format results with error handling
            formatted_results = []
            try:
                for result in search_results[0]:
                    try:
                        # Get field values using correct pymilvus API for version 2.5+
                        def safe_get_field(result_obj, field_name, default=''):
                            try:
                                if hasattr(result_obj, 'entity') and result_obj.entity:
                                    return getattr(result_obj.entity, field_name, default)
                                elif hasattr(result_obj, field_name):
                                    return getattr(result_obj, field_name, default)
                                else:
                                    return default
                            except Exception:
                                return default

                        description_url = safe_get_field(result, 'description_url', '')
                        web_url = safe_get_field(result, 'web_url', '')

                        # Determine URL based on description_url availability
                        url = description_url if description_url and description_url.strip() else web_url

                        formatted_result = {
                            "score": float(result.score) if hasattr(result, 'score') else 0.0,
                            "chunk_text": safe_get_field(result, 'chunk_text', ''),
                            "file_name": safe_get_field(result, 'file_name', ''),
                            "page_number": safe_get_field(result, 'page_number', ''),
                           # "chunk_id": int(safe_get_field(result, 'chunk_id', 0)) if safe_get_field(result, 'chunk_id', 0) is not None else 0,
                           # "mime_type": safe_get_field(result, 'mime_type', ''),
                           # "modified_time": safe_get_field(result, 'modified_time', ''),
                           # "last_modified_by": safe_get_field(result, 'last_modified_by', ''),
                           # "description_url": description_url,
                           # "web_url": web_url,
                           # "drive_path": safe_get_field(result, 'drive_path', ''),
                           # "doc_id": safe_get_field(result, 'doc_id', ''),
                            #"table_id": safe_get_field(result, 'table_id', ''),
                            "topic": safe_get_field(result, 'topic', ''),
                            #"hyperlinks": safe_get_field(result, 'hyperlinks', ''),
                            "Document_URL": url  # New parameter: description_url if not null/empty, else web_url
                        }
                        formatted_results.append(formatted_result)
                    except Exception as format_error:
                        logger.warning(f"Error formatting search result: {format_error}")
                        continue

                logger.info(f"Query executed successfully, returned {len(formatted_results)} results")
                return formatted_results

            except Exception as results_error:
                logger.error(f"Error processing search results: {results_error}")
                return []

        except ValueError as ve:
            logger.error(f"Query validation error: {ve}")
            raise
        except ConnectionError as ce:
            logger.error(f"Connection error during query: {ce}")
            raise
        except Exception as e:
            logger.error(f"Query failed: {e}")
            raise RuntimeError(f"Query execution failed: {str(e)}")

    def disconnect(self):
        """Disconnect from Milvus"""
        try:
            if self.is_connected:
                connections.disconnect("default")
                self.is_connected = False
                self.collection_exists = False
                self.collection = None
                logger.info("Disconnected from Milvus")
        except Exception as e:
            logger.error(f"Error disconnecting from Milvus: {e}")

    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.disconnect()
        except Exception:
            pass  # Ignore errors during cleanup


# Singleton instances for reuse
_milvus_service_instances = {}

def get_milvus_service(collection_name: str) -> MilvusService:
    """Get or create a singleton MilvusService instance for the given collection with health checking"""

    # Check if instance exists and is healthy
    if collection_name in _milvus_service_instances:
        service = _milvus_service_instances[collection_name]

        # Check connection health
        if service.is_connected:
            # Quick health check - verify connection is still alive
            try:
                if connections.has_connection("default"):
                    # Connection exists, return cached instance
                    logger.debug(f"Reusing healthy service instance for {collection_name}")
                    return service
                else:
                    logger.debug(f"Connection lost for {collection_name}, reconnecting...")
                    service.is_connected = False
            except Exception as e:
                logger.debug(f"Connection health check failed for {collection_name}: {e}")
                service.is_connected = False

    # Create new instance or reinitialize existing one
    if collection_name not in _milvus_service_instances:
        logger.debug(f"Creating new service instance for {collection_name}")
        _milvus_service_instances[collection_name] = MilvusService(collection_name)

    service = _milvus_service_instances[collection_name]

    # Ensure connection and model are initialized
    if not service.is_connected:
        try:
            logger.debug(f"Initializing connection for {collection_name}")
            service.connect_milvus()
        except Exception as e:
            logger.warning(f"Failed to connect service for {collection_name}: {e}")

    # Ensure embedding model is initialized (uses class-level cache)
    if service.embed_model is None:
        try:
            logger.debug(f"Initializing embedding model for {collection_name}")
            service._initialize_embedding_model()
        except Exception as e:
            logger.warning(f"Failed to initialize embedding model for {collection_name}: {e}")

    return service
