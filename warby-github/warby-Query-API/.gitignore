# Ignore macOS system files
.DS_Store

# Ignore accidental extra readme
README 2.md
PERFORMANCE_OPTIMIZATIONS.md

# Ignore local environment files
#config.env

# Ignore debug/test scripts
debug_milvus_connection.py
start_server.py
create_collection.py
.pyc
*.pyc
__pycache__/
*.log
askhr.py
askit.py
*.env

# Ignore test files and reports created during testing
test_comprehensive.py
test_logging.py
test_stress_edge_cases.py
test_report_*.json
stress_test_report_*.json
logging_test_report_*.json
COMPREHENSIVE_TEST_SUMMARY.md
config.env.backup