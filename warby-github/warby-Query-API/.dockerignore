# Virtual environments
warby-venv/
venv/
env/
.env

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
*.md

# Test files
test_*.py
debug_*.py
*_test.py

# Logs (will be created in container)
logs/
*.log

# Development scripts
run.sh
start_server.py

# Docker files
Dockerfile*
docker-compose*.yml

# Temporary files
*.tmp
*.temp

*.pyc
__pycache__/
.dockerignore
.git
.gitignore
.vscode
.env
config.env