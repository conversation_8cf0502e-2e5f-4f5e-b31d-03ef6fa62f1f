from fastapi import APIRouter
from fastapi.responses import JSONResponse
from loguru import logger
from app.core.config import settings

router = APIRouter()

@router.get("/health")
async def health_check():
    """Health check endpoint with Milvus connection status for askHR and askIT collections"""
    try:
        from app.services.milvus_service import get_milvus_service

        # Check askHR collection using singleton
        askhr_service = get_milvus_service(settings.MILVUS_ASKHR_COLLECTION)
        askhr_health = askhr_service.health_check()

        # Check askIT collection using singleton
        askit_service = get_milvus_service(settings.MILVUS_ASKIT_COLLECTION)
        askit_health = askit_service.health_check()

        # Determine overall status
        statuses = {askhr_health.get("status"), askit_health.get("status")}
        if statuses == {"OK"}:
            http_status = 200
            overall_status = "OK"
            message = "All collections healthy"
        elif "error" in statuses or "disconnected" in statuses:
            http_status = 503
            overall_status = "unhealthy"
            message = "Service is not functioning properly"
        else:
            http_status = 503
            overall_status = "degraded"
            message = "One or more collections are missing or degraded"

        return JSONResponse(
            status_code=http_status,
            content={
                "status": overall_status,
                "app": "warbyparker-retrival",
                "milvus": {
                    "askhr": askhr_health,
                    "askit": askit_health
                },
                "message": message
            }
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "critical",
                "app": "warbyparker-retrival",
                "milvus": {"status": "error", "error": str(e)},
                "message": "Service is experiencing critical issues"
            }
        )
